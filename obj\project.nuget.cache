{"version": 2, "dgSpecHash": "LBlCBAQ7xSw=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\TodoApi\\TodoApi.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.diagnostics.entityframeworkcore\\9.0.6\\microsoft.aspnetcore.diagnostics.entityframeworkcore.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore\\9.0.6\\microsoft.entityframeworkcore.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.abstractions\\9.0.6\\microsoft.entityframeworkcore.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.analyzers\\9.0.6\\microsoft.entityframeworkcore.analyzers.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.inmemory\\9.0.6\\microsoft.entityframeworkcore.inmemory.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.relational\\9.0.6\\microsoft.entityframeworkcore.relational.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.apidescription.server\\8.0.14\\microsoft.extensions.apidescription.server.8.0.14.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\9.0.6\\microsoft.extensions.caching.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\9.0.6\\microsoft.extensions.caching.memory.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.6\\microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.6\\microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.6\\microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\9.0.0\\microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.embedded\\9.0.0\\microsoft.extensions.fileproviders.embedded.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.6\\microsoft.extensions.logging.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.6\\microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.6\\microsoft.extensions.options.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.6\\microsoft.extensions.primitives.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\namotion.reflection\\3.4.2\\namotion.reflection.3.4.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\njsonschema\\11.3.2\\njsonschema.11.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\njsonschema.annotations\\11.3.2\\njsonschema.annotations.11.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\njsonschema.newtonsoftjson\\11.3.2\\njsonschema.newtonsoftjson.11.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\njsonschema.yaml\\11.3.2\\njsonschema.yaml.11.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nswag.annotations\\14.4.0\\nswag.annotations.14.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nswag.aspnetcore\\14.4.0\\nswag.aspnetcore.14.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nswag.core\\14.4.0\\nswag.core.14.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nswag.core.yaml\\14.4.0\\nswag.core.yaml.14.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nswag.generation\\14.4.0\\nswag.generation.14.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nswag.generation.aspnetcore\\14.4.0\\nswag.generation.aspnetcore.14.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\yamldotnet\\16.3.0\\yamldotnet.16.3.0.nupkg.sha512"], "logs": []}