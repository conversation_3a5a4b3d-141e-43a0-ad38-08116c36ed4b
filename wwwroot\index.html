<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todo App</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .add-todo {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
        }
        input[type="text"] {
            flex: 1;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            padding: 12px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button.delete {
            background: #dc3545;
            padding: 5px 10px;
            font-size: 12px;
        }
        button.delete:hover {
            background: #c82333;
        }
        .todo-list {
            list-style: none;
            padding: 0;
        }
        .todo-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .todo-item.completed {
            opacity: 0.7;
            border-left-color: #28a745;
        }
        .todo-item input[type="checkbox"] {
            margin-right: 15px;
            transform: scale(1.2);
        }
        .todo-text {
            flex: 1;
            font-size: 16px;
        }
        .todo-item.completed .todo-text {
            text-decoration: line-through;
            color: #6c757d;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📝 Todo App</h1>
        
        <div class="add-todo">
            <input type="text" id="todoInput" placeholder="Nhập công việc cần làm..." />
            <button onclick="addTodo()">Thêm</button>
        </div>
        
        <div id="error" class="error" style="display: none;"></div>
        <div id="loading" class="loading" style="display: none;">Đang tải...</div>
        
        <ul id="todoList" class="todo-list"></ul>
    </div>

    <script>
        const API_BASE = 'http://localhost:5200/todoitems';
        
        // Load todos when page loads
        document.addEventListener('DOMContentLoaded', loadTodos);
        
        // Add enter key support
        document.getElementById('todoInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                addTodo();
            }
        });
        
        async function loadTodos() {
            showLoading(true);
            hideError();
            
            try {
                const response = await fetch(API_BASE);
                if (!response.ok) throw new Error('Không thể tải danh sách todo');
                
                const todos = await response.json();
                displayTodos(todos);
            } catch (error) {
                showError('Lỗi khi tải danh sách: ' + error.message);
            } finally {
                showLoading(false);
            }
        }
        
        async function addTodo() {
            const input = document.getElementById('todoInput');
            const name = input.value.trim();
            
            if (!name) {
                showError('Vui lòng nhập tên công việc');
                return;
            }
            
            hideError();
            
            try {
                const response = await fetch(API_BASE, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: name,
                        isComplete: false
                    })
                });
                
                if (!response.ok) throw new Error('Không thể thêm todo');
                
                input.value = '';
                loadTodos(); // Reload the list
            } catch (error) {
                showError('Lỗi khi thêm todo: ' + error.message);
            }
        }
        
        async function toggleTodo(id, isComplete) {
            hideError();
            
            try {
                // First get the current todo to get its name
                const getResponse = await fetch(`${API_BASE}/${id}`);
                if (!getResponse.ok) throw new Error('Không thể lấy thông tin todo');
                
                const todo = await getResponse.json();
                
                // Then update it
                const response = await fetch(`${API_BASE}/${id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: id,
                        name: todo.name,
                        isComplete: isComplete
                    })
                });
                
                if (!response.ok) throw new Error('Không thể cập nhật todo');
                
                loadTodos(); // Reload the list
            } catch (error) {
                showError('Lỗi khi cập nhật todo: ' + error.message);
            }
        }
        
        async function deleteTodo(id) {
            if (!confirm('Bạn có chắc muốn xóa công việc này?')) return;
            
            hideError();
            
            try {
                const response = await fetch(`${API_BASE}/${id}`, {
                    method: 'DELETE'
                });
                
                if (!response.ok) throw new Error('Không thể xóa todo');
                
                loadTodos(); // Reload the list
            } catch (error) {
                showError('Lỗi khi xóa todo: ' + error.message);
            }
        }
        
        function displayTodos(todos) {
            const todoList = document.getElementById('todoList');
            todoList.innerHTML = '';
            
            if (todos.length === 0) {
                todoList.innerHTML = '<li style="text-align: center; color: #666; font-style: italic;">Chưa có công việc nào</li>';
                return;
            }
            
            todos.forEach(todo => {
                const li = document.createElement('li');
                li.className = `todo-item ${todo.isComplete ? 'completed' : ''}`;
                li.innerHTML = `
                    <input type="checkbox" ${todo.isComplete ? 'checked' : ''} 
                           onchange="toggleTodo(${todo.id}, this.checked)">
                    <span class="todo-text">${escapeHtml(todo.name)}</span>
                    <button class="delete" onclick="deleteTodo(${todo.id})">Xóa</button>
                `;
                todoList.appendChild(li);
            });
        }
        
        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        
        function hideError() {
            document.getElementById('error').style.display = 'none';
        }
        
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>
</html>
