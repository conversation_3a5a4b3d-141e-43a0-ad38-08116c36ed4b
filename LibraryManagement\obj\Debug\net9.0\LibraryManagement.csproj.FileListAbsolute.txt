C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\LibraryManagement.csproj.AssemblyReference.cache
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\LibraryManagement.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\LibraryManagement.AssemblyInfoInputs.cache
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\LibraryManagement.AssemblyInfo.cs
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\LibraryManagement.csproj.CoreCompileInputs.cache
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\LibraryManagement.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\LibraryManagement.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\appsettings.Development.json
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\appsettings.json
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\LibraryManagement.staticwebassets.runtime.json
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\LibraryManagement.staticwebassets.endpoints.json
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\LibraryManagement.exe
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\LibraryManagement.deps.json
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\LibraryManagement.runtimeconfig.json
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\LibraryManagement.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\LibraryManagement.pdb
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Microsoft.AspNetCore.OpenApi.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.InMemory.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Microsoft.OpenApi.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Namotion.Reflection.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Newtonsoft.Json.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\NJsonSchema.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\NJsonSchema.Annotations.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\NJsonSchema.NewtonsoftJson.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\NJsonSchema.Yaml.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\NSwag.Annotations.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\NSwag.AspNetCore.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\NSwag.Core.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\NSwag.Core.Yaml.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\NSwag.Generation.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\NSwag.Generation.AspNetCore.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\TodoApi\LibraryManagement\bin\Debug\net9.0\YamlDotNet.dll
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\scopedcss\bundle\LibraryManagement.styles.css
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\compressed\5aes2t1s1r-o4tbd18gej.gz
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\staticwebassets.build.json
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\staticwebassets.development.json
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\staticwebassets\msbuild.LibraryManagement.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\staticwebassets\msbuild.LibraryManagement.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\staticwebassets\msbuild.build.LibraryManagement.props
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\staticwebassets\msbuild.buildMultiTargeting.LibraryManagement.props
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\staticwebassets\msbuild.buildTransitive.LibraryManagement.props
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\staticwebassets.pack.json
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\LibraryM.685EF3B5.Up2Date
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\LibraryManagement.dll
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\refint\LibraryManagement.dll
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\LibraryManagement.pdb
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\LibraryManagement.genruntimeconfig.cache
C:\Users\<USER>\TodoApi\LibraryManagement\obj\Debug\net9.0\ref\LibraryManagement.dll
