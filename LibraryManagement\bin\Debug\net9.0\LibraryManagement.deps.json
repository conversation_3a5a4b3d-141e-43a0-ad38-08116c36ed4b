{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"LibraryManagement/1.0.0": {"dependencies": {"Microsoft.AspNetCore.OpenApi": "9.0.3", "Microsoft.EntityFrameworkCore.InMemory": "9.0.6", "NSwag.AspNetCore": "14.4.0", "Swashbuckle.AspNetCore": "8.1.4"}, "runtime": {"LibraryManagement.dll": {}}}, "Microsoft.AspNetCore.OpenApi/9.0.3": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.325.11220"}}}, "Microsoft.EntityFrameworkCore/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.6", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.6": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.6": {}, "Microsoft.EntityFrameworkCore.InMemory/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.InMemory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26607"}}}, "Microsoft.Extensions.ApiDescription.Server/8.0.14": {}, "Microsoft.Extensions.Caching.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Caching.Memory/9.0.6": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.FileProviders.Embedded/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0"}}, "Microsoft.Extensions.Logging/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Options/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Primitives/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.OpenApi/1.6.23": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.23.0", "fileVersion": "1.6.23.0"}}}, "Namotion.Reflection/3.4.2": {"runtime": {"lib/net8.0/Namotion.Reflection.dll": {"assemblyVersion": "3.4.2.0", "fileVersion": "3.4.2.0"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "NJsonSchema/11.3.2": {"dependencies": {"NJsonSchema.Annotations": "11.3.2", "Namotion.Reflection": "3.4.2", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/NJsonSchema.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NJsonSchema.Annotations/11.3.2": {"runtime": {"lib/netstandard2.0/NJsonSchema.Annotations.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NJsonSchema.NewtonsoftJson/11.3.2": {"dependencies": {"NJsonSchema": "11.3.2", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/NJsonSchema.NewtonsoftJson.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NJsonSchema.Yaml/11.3.2": {"dependencies": {"NJsonSchema": "11.3.2", "YamlDotNet": "16.3.0"}, "runtime": {"lib/net8.0/NJsonSchema.Yaml.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NSwag.Annotations/14.4.0": {"runtime": {"lib/netstandard2.0/NSwag.Annotations.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NSwag.AspNetCore/14.4.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "8.0.14", "Microsoft.Extensions.FileProviders.Embedded": "9.0.0", "NSwag.Annotations": "14.4.0", "NSwag.Core": "14.4.0", "NSwag.Core.Yaml": "14.4.0", "NSwag.Generation": "14.4.0", "NSwag.Generation.AspNetCore": "14.4.0"}, "runtime": {"lib/net9.0/NSwag.AspNetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NSwag.Core/14.4.0": {"dependencies": {"NJsonSchema": "11.3.2"}, "runtime": {"lib/net8.0/NSwag.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NSwag.Core.Yaml/14.4.0": {"dependencies": {"NJsonSchema.Yaml": "11.3.2", "NSwag.Core": "14.4.0"}, "runtime": {"lib/net8.0/NSwag.Core.Yaml.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NSwag.Generation/14.4.0": {"dependencies": {"NJsonSchema.NewtonsoftJson": "11.3.2", "NSwag.Core": "14.4.0"}, "runtime": {"lib/net8.0/NSwag.Generation.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "NSwag.Generation.AspNetCore/14.4.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "NSwag.Generation": "14.4.0"}, "runtime": {"lib/net9.0/NSwag.Generation.AspNetCore.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Swashbuckle.AspNetCore/8.1.4": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "8.0.14", "Swashbuckle.AspNetCore.Swagger": "8.1.4", "Swashbuckle.AspNetCore.SwaggerGen": "8.1.4", "Swashbuckle.AspNetCore.SwaggerUI": "8.1.4"}}, "Swashbuckle.AspNetCore.Swagger/8.1.4": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.4.1479"}}}, "Swashbuckle.AspNetCore.SwaggerGen/8.1.4": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "8.1.4"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.4.1479"}}}, "Swashbuckle.AspNetCore.SwaggerUI/8.1.4": {"runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "8.1.4.1479"}}}, "YamlDotNet/16.3.0": {"runtime": {"lib/net8.0/YamlDotNet.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}}}, "libraries": {"LibraryManagement/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.OpenApi/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-fKh0UyGMUE+lhbovMhh3g88b9bT+y2jfZIuJ8ljY7rcCaSJ9m2Qqqbh66oULFfzWE2BUAmimzTGcPcq3jXi/Ew==", "path": "microsoft.aspnetcore.openapi/9.0.3", "hashPath": "microsoft.aspnetcore.openapi.9.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-r5hzM6Bhw4X3z28l5vmsaCPjk9VsQP4zaaY01THh1SAYjgTMVadYIvpNkCfmrv/Klks6aIf2A9eY7cpGZab/hg==", "path": "microsoft.entityframeworkcore/9.0.6", "hashPath": "microsoft.entityframeworkcore.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-7MkhPK8emb8hfOx/mFVvHuIHxQ+mH2YdlK4sFUXgsGlvR0A44vsmd2wcHavZOTTzaKhN+aFUVy3zmkztKmTo+A==", "path": "microsoft.entityframeworkcore.abstractions/9.0.6", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VKggHNQC5FCn3/vooaIM/4aEjGmrmWm78IrdRLz9lLV0Rm9bVHEr/jiWApDkU0U9ec2xGAilvQqJ5mMX7QC2cw==", "path": "microsoft.entityframeworkcore.analyzers/9.0.6", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.InMemory/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-y7cpyB/aYVi4VZiVoUcIA2vQLlypQv1wGmly47RwXvPX15S15mooBsxyVmw3BShBXbdnK5wxfslihIoXzplnGg==", "path": "microsoft.entityframeworkcore.inmemory/9.0.6", "hashPath": "microsoft.entityframeworkcore.inmemory.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/8.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-wH+yLZAhfDUnlz8gFGFzDhmYOG9Yb9KTKENENm65E9nmRu2oZ1eTtb2N3x6qgDZ4X3pISiW1PxD/HfSpxMBpNw==", "path": "microsoft.extensions.apidescription.server/8.0.14", "hashPath": "microsoft.extensions.apidescription.server.8.0.14.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-bL/xQsVNrdVkzjP5yjX4ndkQ03H3+Bk3qPpl+AMCEJR2RkfgAYmoQ/xXffPV7is64+QHShnhA12YAaFmNbfM+A==", "path": "microsoft.extensions.caching.abstractions/9.0.6", "hashPath": "microsoft.extensions.caching.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-qPW2d798tBPZcRmrlaBJqyChf2+0odDdE+0Lxvrr0ywkSNl1oNMK8AKrOfDwyXyjuLCv0ua7p6nrUExCeXhCcg==", "path": "microsoft.extensions.caching.memory/9.0.6", "hashPath": "microsoft.extensions.caching.memory.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "path": "microsoft.extensions.dependencyinjection/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uK439QzYR0q2emLVtYzwyK3x+T5bTY4yWsd/k/ZUS9LR6Sflp8MIdhGXW8kQCd86dQD4tLqvcbLkku8qHY263Q==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6Ev1goLIvggLF6uCs6oZvdr9JM+2b1Zj+4FLdBWNW5iw3tm2BymVIb0yMsjnQTBWL7YUmqVWH3u45hSqOfvuqg==", "path": "microsoft.extensions.fileproviders.embedded/9.0.0", "hashPath": "microsoft.extensions.fileproviders.embedded.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "path": "microsoft.extensions.logging/9.0.6", "hashPath": "microsoft.extensions.logging.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "path": "microsoft.extensions.logging.abstractions/9.0.6", "hashPath": "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "path": "microsoft.extensions.options/9.0.6", "hashPath": "microsoft.extensions.options.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "path": "microsoft.extensions.primitives/9.0.6", "hashPath": "microsoft.extensions.primitives.9.0.6.nupkg.sha512"}, "Microsoft.OpenApi/1.6.23": {"type": "package", "serviceable": true, "sha512": "sha512-tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g==", "path": "microsoft.openapi/1.6.23", "hashPath": "microsoft.openapi.1.6.23.nupkg.sha512"}, "Namotion.Reflection/3.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-ZHrvPdAg7zV78iOTiH9ua+34rBfn4iH6Bjfo2bzUHOGD3KkjGUvqxBFy+v9p6qwV+GEeYWl4NOqXH8tVcZOMpw==", "path": "namotion.reflection/3.4.2", "hashPath": "namotion.reflection.3.4.2.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "NJsonSchema/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QXvelMLKz1NsMPc0HndaaxryNIV1V+AFYuZV9w3H6e+03jp3f3n1w8XLcIaA5WA51EHQdZEP4V2Bfgl6kpxDKg==", "path": "njsonschema/11.3.2", "hashPath": "njsonschema.11.3.2.nupkg.sha512"}, "NJsonSchema.Annotations/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-rSwQFKdLlq/lbAJfYqI5KBE46KJCbc99L6G9XM1nVpCOxE0eoaNE8+fkJ7Ws+I5VqP7oEPGKXTR3Q7PmYoeTDA==", "path": "njsonschema.annotations/11.3.2", "hashPath": "njsonschema.annotations.11.3.2.nupkg.sha512"}, "NJsonSchema.NewtonsoftJson/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-qb93cjF/X6ifcdVfMjDV0ItfN1/7np2buKOn7pZqOBRBA0gU5TzKw4lJtblA7EQh2hXqx7ptDcbuYGKfaYfLWw==", "path": "njsonschema.newtonsoftjson/11.3.2", "hashPath": "njsonschema.newtonsoftjson.11.3.2.nupkg.sha512"}, "NJsonSchema.Yaml/11.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xGPr+gJYcNquAN2YTFyLZvJPRO01nCmuzU8vvxozTEqYO97JRo/Lip0JrulVyqZoaqnUCtbCvgIRCrS/4XqWaw==", "path": "njsonschema.yaml/11.3.2", "hashPath": "njsonschema.yaml.11.3.2.nupkg.sha512"}, "NSwag.Annotations/14.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2K/V0IJz1ffJ+A5hvaPkE6TAP4uty2U2e4dOom0LD69hG6iKNF7aEfVMMeLJgGcspl8B+W7X2Ys+RPy74SJnPg==", "path": "nswag.annotations/14.4.0", "hashPath": "nswag.annotations.14.4.0.nupkg.sha512"}, "NSwag.AspNetCore/14.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-kgnCLBfb4Giih/wkHWU1XVPEvV43Q7OLqMk3/h88nuH2e1JmkBlRf49Pxx1jL87X+0Ewj45myjT5jfCgoLsPFA==", "path": "nswag.aspnetcore/14.4.0", "hashPath": "nswag.aspnetcore.14.4.0.nupkg.sha512"}, "NSwag.Core/14.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-Zo79LJPCJa2KcD2BblRGTQJp7c2ZduZZg0xeI+D8hmcfgHpuQhOHYdd2WSS+cYfO2sEZVtnsbrYM+SGvKvNMCw==", "path": "nswag.core/14.4.0", "hashPath": "nswag.core.14.4.0.nupkg.sha512"}, "NSwag.Core.Yaml/14.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-qb80vaks2eX6l1c6a8N1xxgUOGJvRrEKzTL1VS6oR1gP9R4+/pZFCuHF0uFvw9x9CmpF2UwbuZWdylz5KkrE+g==", "path": "nswag.core.yaml/14.4.0", "hashPath": "nswag.core.yaml.14.4.0.nupkg.sha512"}, "NSwag.Generation/14.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-ll9EvxxwBlcJiAUAOWtgYN3N8hKuzR7sHPfv7VmVy7Joyin3sjAaY/+tBBBi1s/NydTxUPpZ+IlThEaFFc7xfA==", "path": "nswag.generation/14.4.0", "hashPath": "nswag.generation.14.4.0.nupkg.sha512"}, "NSwag.Generation.AspNetCore/14.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-b1+qdKuThjkuJkL72QiL0ziGiR39O4DgJY7f3C+MnmRGGRjXj4BoEKvJXkZyi6Dn2M7Li/vMVKoHQCaQjrYwlA==", "path": "nswag.generation.aspnetcore/14.4.0", "hashPath": "nswag.generation.aspnetcore.14.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-qYk8VHyvs6wML+KXtjyCgS9Aj18mcm0ZtnJeNCTlj/DYQ7A3pfLIztQgLuZS/LEMYsrTo1lSKR3IIZ5/HzVCWA==", "path": "swashbuckle.aspnetcore/8.1.4", "hashPath": "swashbuckle.aspnetcore.8.1.4.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-w83aYEBJYNa6ZYomziwZWwXhqQPLKhZH0n8MzqqNhF1ElCGBKm71kd7W6pgIr/yu0i6ymQzrZUFSZLdvH1kY5w==", "path": "swashbuckle.aspnetcore.swagger/8.1.4", "hashPath": "swashbuckle.aspnetcore.swagger.8.1.4.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-aBwO2MF1HHAaWgdBwX8tlSqxycOKTKmCT6pEpb0oSY1pn7mUdmzJvHZA0HxWx9nfmKP0eOGQcLC9ZnN/MuehRQ==", "path": "swashbuckle.aspnetcore.swaggergen/8.1.4", "hashPath": "swashbuckle.aspnetcore.swaggergen.8.1.4.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/8.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-mTn6OwB43ETrN6IgAZd7ojWGhTwBZ98LT3QwbAn6Gg3wJStQV4znU0mWiHaKFlD/+Qhj1uhAUOa52rmd6xmbzg==", "path": "swashbuckle.aspnetcore.swaggerui/8.1.4", "hashPath": "swashbuckle.aspnetcore.swaggerui.8.1.4.nupkg.sha512"}, "YamlDotNet/16.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SgMOdxbz8X65z8hraIs6hOEdnkH6hESTAIUa7viEngHOYaH+6q5XJmwr1+yb9vJpNQ19hCQY69xbFsLtXpobQA==", "path": "yamldotnet/16.3.0", "hashPath": "yamldotnet.16.3.0.nupkg.sha512"}}}