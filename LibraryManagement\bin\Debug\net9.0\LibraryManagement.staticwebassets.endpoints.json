{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "create-sample-images.7kp85wy5fe.html", "AssetFile": "create-sample-images.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000677506775"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1475"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"LnBfrQqyHg9cZ2mPfuIrVFYrUOZwWahBVdme+0WAVsM=\""}, {"Name": "ETag", "Value": "W/\"ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7kp85wy5fe"}, {"Name": "integrity", "Value": "sha256-ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A="}, {"Name": "label", "Value": "create-sample-images.html"}]}, {"Route": "create-sample-images.7kp85wy5fe.html", "AssetFile": "create-sample-images.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5394"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:33 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7kp85wy5fe"}, {"Name": "integrity", "Value": "sha256-ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A="}, {"Name": "label", "Value": "create-sample-images.html"}]}, {"Route": "create-sample-images.7kp85wy5fe.html.gz", "AssetFile": "create-sample-images.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1475"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"LnBfrQqyHg9cZ2mPfuIrVFYrUOZwWahBVdme+0WAVsM=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7kp85wy5fe"}, {"Name": "integrity", "Value": "sha256-LnBfrQqyHg9cZ2mPfuIrVFYrUOZwWahBVdme+0WAVsM="}, {"Name": "label", "Value": "create-sample-images.html.gz"}]}, {"Route": "create-sample-images.html", "AssetFile": "create-sample-images.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000677506775"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1475"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"LnBfrQqyHg9cZ2mPfuIrVFYrUOZwWahBVdme+0WAVsM=\""}, {"Name": "ETag", "Value": "W/\"ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A="}]}, {"Route": "create-sample-images.html", "AssetFile": "create-sample-images.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5394"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:33 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A="}]}, {"Route": "create-sample-images.html.gz", "AssetFile": "create-sample-images.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1475"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"LnBfrQqyHg9cZ2mPfuIrVFYrUOZwWahBVdme+0WAVsM=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:33 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LnBfrQqyHg9cZ2mPfuIrVFYrUOZwWahBVdme+0WAVsM="}]}, {"Route": "index.ho564kftnr.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000149409831"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6692"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"EGrqTN4Cf8xmhpzaeQYU5D+T3sY9Z03QpAC7i/Kq7i0=\""}, {"Name": "ETag", "Value": "W/\"QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:00 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ho564kftnr"}, {"Name": "integrity", "Value": "sha256-QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.ho564kftnr.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "39263"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:00 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ho564kftnr"}, {"Name": "integrity", "Value": "sha256-QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.ho564kftnr.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6692"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"EGrqTN4Cf8xmhpzaeQYU5D+T3sY9Z03QpAC7i/Kq7i0=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:00 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ho564kftnr"}, {"Name": "integrity", "Value": "sha256-EGrqTN4Cf8xmhpzaeQYU5D+T3sY9Z03QpAC7i/Kq7i0="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000149409831"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6692"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"EGrqTN4Cf8xmhpzaeQYU5D+T3sY9Z03QpAC7i/Kq7i0=\""}, {"Name": "ETag", "Value": "W/\"QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:00 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "39263"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:00 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6692"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"EGrqTN4Cf8xmhpzaeQYU5D+T3sY9Z03QpAC7i/Kq7i0=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:00 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EGrqTN4Cf8xmhpzaeQYU5D+T3sY9Z03QpAC7i/Kq7i0="}]}, {"Route": "upload.hdb828yqv9.html", "AssetFile": "upload.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000514668039"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1942"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"kNWgX1uuvweeH2Zd5pFK2vz/2gfTq+mJ22gbp8GgQG4=\""}, {"Name": "ETag", "Value": "W/\"axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:44:29 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hdb828yqv9"}, {"Name": "integrity", "Value": "sha256-axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8="}, {"Name": "label", "Value": "upload.html"}]}, {"Route": "upload.hdb828yqv9.html", "AssetFile": "upload.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5925"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:44:29 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hdb828yqv9"}, {"Name": "integrity", "Value": "sha256-axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8="}, {"Name": "label", "Value": "upload.html"}]}, {"Route": "upload.hdb828yqv9.html.gz", "AssetFile": "upload.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1942"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"kNWgX1uuvweeH2Zd5pFK2vz/2gfTq+mJ22gbp8GgQG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:44:29 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hdb828yqv9"}, {"Name": "integrity", "Value": "sha256-kNWgX1uuvweeH2Zd5pFK2vz/2gfTq+mJ22gbp8GgQG4="}, {"Name": "label", "Value": "upload.html.gz"}]}, {"Route": "upload.html", "AssetFile": "upload.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000514668039"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1942"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"kNWgX1uuvweeH2Zd5pFK2vz/2gfTq+mJ22gbp8GgQG4=\""}, {"Name": "ETag", "Value": "W/\"axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:44:29 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8="}]}, {"Route": "upload.html", "AssetFile": "upload.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5925"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:44:29 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8="}]}, {"Route": "upload.html.gz", "AssetFile": "upload.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1942"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"kNWgX1uuvweeH2Zd5pFK2vz/2gfTq+mJ22gbp8GgQG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:44:29 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kNWgX1uuvweeH2Zd5pFK2vz/2gfTq+mJ22gbp8GgQG4="}]}, {"Route": "vue-library.html", "AssetFile": "vue-library.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.045454545455"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "21"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=\""}, {"Name": "ETag", "Value": "W/\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}]}, {"Route": "vue-library.html", "AssetFile": "vue-library.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}]}, {"Route": "vue-library.html.gz", "AssetFile": "vue-library.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "21"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4="}]}, {"Route": "vue-library.lv5tvmajl9.html", "AssetFile": "vue-library.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.045454545455"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "21"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=\""}, {"Name": "ETag", "Value": "W/\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lv5tvmajl9"}, {"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}, {"Name": "label", "Value": "vue-library.html"}]}, {"Route": "vue-library.lv5tvmajl9.html", "AssetFile": "vue-library.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lv5tvmajl9"}, {"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}, {"Name": "label", "Value": "vue-library.html"}]}, {"Route": "vue-library.lv5tvmajl9.html.gz", "AssetFile": "vue-library.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "21"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lv5tvmajl9"}, {"Name": "integrity", "Value": "sha256-5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4="}, {"Name": "label", "Value": "vue-library.html.gz"}]}]}