using System.ComponentModel.DataAnnotations;

namespace LibraryManagement.Models
{
    public class BookDto
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "Tiêu đề là bắt buộc")]
        [StringLength(200, ErrorMessage = "Tiêu đề không được vượt quá 200 ký tự")]
        public string Title { get; set; } = string.Empty;
        
        [Required(ErrorMessage = "Tác giả là bắt buộc")]
        [StringLength(100, ErrorMessage = "Tên tác giả không được vượt quá 100 ký tự")]
        public string Author { get; set; } = string.Empty;
        
        [StringLength(13, ErrorMessage = "ISBN không được vượt quá 13 ký tự")]
        public string? ISBN { get; set; }
        
        [StringLength(100, ErrorMessage = "Nhà xuất bản không được vượt quá 100 ký tự")]
        public string? Publisher { get; set; }
        
        public DateTime? PublishedDate { get; set; }
        
        [StringLength(50, ErrorMessage = "Thể loại không được vượt quá 50 ký tự")]
        public string? Genre { get; set; }
        
        [Range(1, 10000, ErrorMessage = "Số trang phải từ 1 đến 10000")]
        public int? PageCount { get; set; }
        
        [StringLength(1000, ErrorMessage = "Mô tả không được vượt quá 1000 ký tự")]
        public string? Description { get; set; }
        
        public string? CoverImageUrl { get; set; }
        
        [Range(0, 5, ErrorMessage = "Đánh giá phải từ 0 đến 5")]
        public decimal? Rating { get; set; }
        
        public bool IsAvailable { get; set; } = true;
        
        public DateTime CreatedAt { get; set; }
        
        public DateTime? UpdatedAt { get; set; }

        public BookDto() { }
        
        public BookDto(Book book)
        {
            Id = book.Id;
            Title = book.Title;
            Author = book.Author;
            ISBN = book.ISBN;
            Publisher = book.Publisher;
            PublishedDate = book.PublishedDate;
            Genre = book.Genre;
            PageCount = book.PageCount;
            Description = book.Description;
            CoverImageUrl = book.CoverImageUrl;
            Rating = book.Rating;
            IsAvailable = book.IsAvailable;
            CreatedAt = book.CreatedAt;
            UpdatedAt = book.UpdatedAt;
        }
        
        public Book ToBook()
        {
            return new Book
            {
                Id = Id,
                Title = Title,
                Author = Author,
                ISBN = ISBN,
                Publisher = Publisher,
                PublishedDate = PublishedDate,
                Genre = Genre,
                PageCount = PageCount,
                Description = Description,
                CoverImageUrl = CoverImageUrl,
                Rating = Rating,
                IsAvailable = IsAvailable,
                CreatedAt = CreatedAt,
                UpdatedAt = UpdatedAt
            };
        }
    }
}
