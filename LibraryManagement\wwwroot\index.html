<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> viện</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }
        
        .controls {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .search-bar {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .search-input {
            flex: 1;
            min-width: 250px;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .filter-select {
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            background: white;
            cursor: pointer;
        }
        
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .btn-secondary {
            background: #95a5a6;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        
        .books-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .book-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s;
            position: relative;
        }
        
        .book-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }
        
        .book-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .book-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .book-author {
            color: #7f8c8d;
            font-size: 1.1em;
        }
        
        .availability-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
        }
        
        .available {
            background: #d5f4e6;
            color: #27ae60;
        }
        
        .unavailable {
            background: #fadbd8;
            color: #e74c3c;
        }
        
        .book-details {
            margin: 15px 0;
        }
        
        .book-detail {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.95em;
        }
        
        .book-detail strong {
            color: #2c3e50;
        }
        
        .book-description {
            color: #7f8c8d;
            font-size: 0.95em;
            line-height: 1.4;
            margin: 15px 0;
            max-height: 60px;
            overflow: hidden;
        }
        
        .book-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .rating {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #f39c12;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            min-height: 100px;
            resize: vertical;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
            font-size: 1.2em;
        }
        
        .error {
            background: #fadbd8;
            color: #e74c3c;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #e74c3c;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }
        
        .page-btn {
            padding: 10px 15px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .page-btn:hover, .page-btn.active {
            background: #667eea;
            color: white;
        }
        
        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        @media (max-width: 768px) {
            .books-grid {
                grid-template-columns: 1fr;
            }
            
            .search-bar {
                flex-direction: column;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        const API_BASE = 'http://localhost:5216/api/books';

        function LibraryApp() {
            const [books, setBooks] = useState([]);
            const [loading, setLoading] = useState(false);
            const [error, setError] = useState('');
            const [searchQuery, setSearchQuery] = useState('');
            const [genreFilter, setGenreFilter] = useState('');
            const [availabilityFilter, setAvailabilityFilter] = useState('');
            const [showModal, setShowModal] = useState(false);
            const [editingBook, setEditingBook] = useState(null);
            const [currentPage, setCurrentPage] = useState(1);
            const [totalPages, setTotalPages] = useState(1);
            const [totalBooks, setTotalBooks] = useState(0);

            const [formData, setFormData] = useState({
                title: '',
                author: '',
                isbn: '',
                publisher: '',
                publishedDate: '',
                genre: '',
                pageCount: '',
                description: '',
                coverImageUrl: '',
                rating: '',
                isAvailable: true
            });

            useEffect(() => {
                loadBooks();
            }, [currentPage]);

            const loadBooks = async () => {
                setLoading(true);
                setError('');
                
                try {
                    const response = await fetch(`${API_BASE}?page=${currentPage}&pageSize=6`);
                    if (!response.ok) throw new Error('Không thể tải danh sách sách');
                    
                    const data = await response.json();
                    setBooks(data.books);
                    setTotalPages(data.totalPages);
                    setTotalBooks(data.totalBooks);
                } catch (err) {
                    setError('Lỗi khi tải danh sách sách: ' + err.message);
                } finally {
                    setLoading(false);
                }
            };

            const searchBooks = async () => {
                setLoading(true);
                setError('');
                
                try {
                    const params = new URLSearchParams();
                    if (searchQuery) params.append('query', searchQuery);
                    if (genreFilter) params.append('genre', genreFilter);
                    if (availabilityFilter) params.append('available', availabilityFilter);
                    
                    const response = await fetch(`${API_BASE}/search?${params}`);
                    if (!response.ok) throw new Error('Không thể tìm kiếm sách');
                    
                    const data = await response.json();
                    setBooks(data);
                    setCurrentPage(1);
                    setTotalPages(1);
                } catch (err) {
                    setError('Lỗi khi tìm kiếm: ' + err.message);
                } finally {
                    setLoading(false);
                }
            };

            const openModal = (book = null) => {
                if (book) {
                    setEditingBook(book);
                    setFormData({
                        title: book.title,
                        author: book.author,
                        isbn: book.isbn || '',
                        publisher: book.publisher || '',
                        publishedDate: book.publishedDate ? book.publishedDate.split('T')[0] : '',
                        genre: book.genre || '',
                        pageCount: book.pageCount || '',
                        description: book.description || '',
                        coverImageUrl: book.coverImageUrl || '',
                        rating: book.rating || '',
                        isAvailable: book.isAvailable
                    });
                } else {
                    setEditingBook(null);
                    setFormData({
                        title: '',
                        author: '',
                        isbn: '',
                        publisher: '',
                        publishedDate: '',
                        genre: '',
                        pageCount: '',
                        description: '',
                        coverImageUrl: '',
                        rating: '',
                        isAvailable: true
                    });
                }
                setShowModal(true);
            };

            const closeModal = () => {
                setShowModal(false);
                setEditingBook(null);
                setError('');
            };

            const handleSubmit = async (e) => {
                e.preventDefault();
                setError('');
                
                try {
                    const bookData = {
                        ...formData,
                        publishedDate: formData.publishedDate ? new Date(formData.publishedDate).toISOString() : null,
                        pageCount: formData.pageCount ? parseInt(formData.pageCount) : null,
                        rating: formData.rating ? parseFloat(formData.rating) : null
                    };

                    const url = editingBook ? `${API_BASE}/${editingBook.id}` : API_BASE;
                    const method = editingBook ? 'PUT' : 'POST';
                    
                    const response = await fetch(url, {
                        method,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(bookData)
                    });
                    
                    if (!response.ok) throw new Error('Không thể lưu sách');
                    
                    closeModal();
                    loadBooks();
                } catch (err) {
                    setError('Lỗi khi lưu sách: ' + err.message);
                }
            };

            const deleteBook = async (id) => {
                if (!confirm('Bạn có chắc muốn xóa sách này?')) return;
                
                try {
                    const response = await fetch(`${API_BASE}/${id}`, {
                        method: 'DELETE'
                    });
                    
                    if (!response.ok) throw new Error('Không thể xóa sách');
                    
                    loadBooks();
                } catch (err) {
                    setError('Lỗi khi xóa sách: ' + err.message);
                }
            };

            const resetFilters = () => {
                setSearchQuery('');
                setGenreFilter('');
                setAvailabilityFilter('');
                setCurrentPage(1);
                loadBooks();
            };

            const genres = [...new Set(books.map(book => book.genre).filter(Boolean))];

            return (
                <div className="container">
                    <div className="header">
                        <h1><i className="fas fa-book"></i> Hệ thống Quản lý Thư viện</h1>
                        <p>Quản lý và tìm kiếm sách một cách dễ dàng</p>
                    </div>

                    <div className="controls">
                        <div className="search-bar">
                            <input
                                type="text"
                                className="search-input"
                                placeholder="Tìm kiếm theo tên sách, tác giả..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                onKeyPress={(e) => e.key === 'Enter' && searchBooks()}
                            />
                            <select
                                className="filter-select"
                                value={genreFilter}
                                onChange={(e) => setGenreFilter(e.target.value)}
                            >
                                <option value="">Tất cả thể loại</option>
                                {genres.map(genre => (
                                    <option key={genre} value={genre}>{genre}</option>
                                ))}
                            </select>
                            <select
                                className="filter-select"
                                value={availabilityFilter}
                                onChange={(e) => setAvailabilityFilter(e.target.value)}
                            >
                                <option value="">Tất cả trạng thái</option>
                                <option value="true">Có sẵn</option>
                                <option value="false">Đã mượn</option>
                            </select>
                            <button className="btn btn-primary" onClick={searchBooks}>
                                <i className="fas fa-search"></i> Tìm kiếm
                            </button>
                            <button className="btn btn-secondary" onClick={resetFilters}>
                                <i className="fas fa-refresh"></i> Reset
                            </button>
                            <button className="btn btn-success" onClick={() => openModal()}>
                                <i className="fas fa-plus"></i> Thêm sách
                            </button>
                        </div>
                        
                        <div style={{textAlign: 'center', color: '#7f8c8d'}}>
                            Tổng số: {totalBooks} sách
                        </div>
                    </div>

                    {error && <div className="error">{error}</div>}

                    {loading ? (
                        <div className="loading">
                            <i className="fas fa-spinner fa-spin"></i> Đang tải...
                        </div>
                    ) : (
                        <>
                            <div className="books-grid">
                                {books.map(book => (
                                    <BookCard 
                                        key={book.id} 
                                        book={book} 
                                        onEdit={() => openModal(book)}
                                        onDelete={() => deleteBook(book.id)}
                                    />
                                ))}
                            </div>

                            {books.length === 0 && !loading && (
                                <div className="loading">
                                    <i className="fas fa-book-open"></i> Không tìm thấy sách nào
                                </div>
                            )}

                            {totalPages > 1 && (
                                <div className="pagination">
                                    <button 
                                        className="page-btn" 
                                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                        disabled={currentPage === 1}
                                    >
                                        <i className="fas fa-chevron-left"></i>
                                    </button>
                                    
                                    {[...Array(totalPages)].map((_, i) => (
                                        <button
                                            key={i + 1}
                                            className={`page-btn ${currentPage === i + 1 ? 'active' : ''}`}
                                            onClick={() => setCurrentPage(i + 1)}
                                        >
                                            {i + 1}
                                        </button>
                                    ))}
                                    
                                    <button 
                                        className="page-btn" 
                                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                                        disabled={currentPage === totalPages}
                                    >
                                        <i className="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                            )}
                        </>
                    )}

                    {showModal && (
                        <BookModal
                            book={editingBook}
                            formData={formData}
                            setFormData={setFormData}
                            onSubmit={handleSubmit}
                            onClose={closeModal}
                            error={error}
                        />
                    )}
                </div>
            );
        }

        function BookCard({ book, onEdit, onDelete }) {
            return (
                <div className="book-card">
                    <div className="book-header">
                        <div>
                            <div className="book-title">{book.title}</div>
                            <div className="book-author">bởi {book.author}</div>
                        </div>
                        <span className={`availability-badge ${book.isAvailable ? 'available' : 'unavailable'}`}>
                            {book.isAvailable ? 'Có sẵn' : 'Đã mượn'}
                        </span>
                    </div>

                    <div className="book-details">
                        {book.genre && (
                            <div className="book-detail">
                                <span>Thể loại:</span>
                                <strong>{book.genre}</strong>
                            </div>
                        )}
                        {book.publisher && (
                            <div className="book-detail">
                                <span>Nhà xuất bản:</span>
                                <strong>{book.publisher}</strong>
                            </div>
                        )}
                        {book.pageCount && (
                            <div className="book-detail">
                                <span>Số trang:</span>
                                <strong>{book.pageCount}</strong>
                            </div>
                        )}
                        {book.rating && (
                            <div className="book-detail">
                                <span>Đánh giá:</span>
                                <div className="rating">
                                    <strong>{book.rating}</strong>
                                    <i className="fas fa-star"></i>
                                </div>
                            </div>
                        )}
                    </div>

                    {book.description && (
                        <div className="book-description">
                            {book.description}
                        </div>
                    )}

                    <div className="book-actions">
                        <button className="btn btn-primary" onClick={onEdit}>
                            <i className="fas fa-edit"></i> Sửa
                        </button>
                        <button className="btn btn-danger" onClick={onDelete}>
                            <i className="fas fa-trash"></i> Xóa
                        </button>
                    </div>
                </div>
            );
        }

        function BookModal({ book, formData, setFormData, onSubmit, onClose, error }) {
            const handleChange = (e) => {
                const { name, value, type, checked } = e.target;
                setFormData(prev => ({
                    ...prev,
                    [name]: type === 'checkbox' ? checked : value
                }));
            };

            return (
                <div className="modal" onClick={(e) => e.target.className === 'modal' && onClose()}>
                    <div className="modal-content">
                        <h2>{book ? 'Chỉnh sửa sách' : 'Thêm sách mới'}</h2>
                        
                        {error && <div className="error">{error}</div>}
                        
                        <form onSubmit={onSubmit}>
                            <div className="form-row">
                                <div className="form-group">
                                    <label className="form-label">Tiêu đề *</label>
                                    <input
                                        type="text"
                                        name="title"
                                        className="form-input"
                                        value={formData.title}
                                        onChange={handleChange}
                                        required
                                    />
                                </div>
                                <div className="form-group">
                                    <label className="form-label">Tác giả *</label>
                                    <input
                                        type="text"
                                        name="author"
                                        className="form-input"
                                        value={formData.author}
                                        onChange={handleChange}
                                        required
                                    />
                                </div>
                            </div>

                            <div className="form-row">
                                <div className="form-group">
                                    <label className="form-label">ISBN</label>
                                    <input
                                        type="text"
                                        name="isbn"
                                        className="form-input"
                                        value={formData.isbn}
                                        onChange={handleChange}
                                    />
                                </div>
                                <div className="form-group">
                                    <label className="form-label">Nhà xuất bản</label>
                                    <input
                                        type="text"
                                        name="publisher"
                                        className="form-input"
                                        value={formData.publisher}
                                        onChange={handleChange}
                                    />
                                </div>
                            </div>

                            <div className="form-row">
                                <div className="form-group">
                                    <label className="form-label">Ngày xuất bản</label>
                                    <input
                                        type="date"
                                        name="publishedDate"
                                        className="form-input"
                                        value={formData.publishedDate}
                                        onChange={handleChange}
                                    />
                                </div>
                                <div className="form-group">
                                    <label className="form-label">Thể loại</label>
                                    <input
                                        type="text"
                                        name="genre"
                                        className="form-input"
                                        value={formData.genre}
                                        onChange={handleChange}
                                    />
                                </div>
                            </div>

                            <div className="form-row">
                                <div className="form-group">
                                    <label className="form-label">Số trang</label>
                                    <input
                                        type="number"
                                        name="pageCount"
                                        className="form-input"
                                        value={formData.pageCount}
                                        onChange={handleChange}
                                        min="1"
                                    />
                                </div>
                                <div className="form-group">
                                    <label className="form-label">Đánh giá (0-5)</label>
                                    <input
                                        type="number"
                                        name="rating"
                                        className="form-input"
                                        value={formData.rating}
                                        onChange={handleChange}
                                        min="0"
                                        max="5"
                                        step="0.1"
                                    />
                                </div>
                            </div>

                            <div className="form-group">
                                <label className="form-label">Mô tả</label>
                                <textarea
                                    name="description"
                                    className="form-textarea"
                                    value={formData.description}
                                    onChange={handleChange}
                                    placeholder="Mô tả về cuốn sách..."
                                />
                            </div>

                            <div className="form-group">
                                <label className="form-label">URL ảnh bìa</label>
                                <input
                                    type="url"
                                    name="coverImageUrl"
                                    className="form-input"
                                    value={formData.coverImageUrl}
                                    onChange={handleChange}
                                    placeholder="https://example.com/cover.jpg"
                                />
                            </div>

                            <div className="form-group">
                                <label style={{display: 'flex', alignItems: 'center', gap: '10px'}}>
                                    <input
                                        type="checkbox"
                                        name="isAvailable"
                                        checked={formData.isAvailable}
                                        onChange={handleChange}
                                    />
                                    <span className="form-label" style={{margin: 0}}>Có sẵn</span>
                                </label>
                            </div>

                            <div style={{display: 'flex', gap: '15px', justifyContent: 'flex-end', marginTop: '30px'}}>
                                <button type="button" className="btn btn-secondary" onClick={onClose}>
                                    Hủy
                                </button>
                                <button type="submit" className="btn btn-success">
                                    <i className="fas fa-save"></i> {book ? 'Cập nhật' : 'Thêm'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<LibraryApp />, document.getElementById('root'));
    </script>
</body>
</html>
