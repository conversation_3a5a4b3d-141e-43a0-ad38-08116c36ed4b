{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000181686047"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5503"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"DxXg5bXQ4gxLJqMY676LqOSwiLPgPBUr0RIcxebdQQs=\""}, {"Name": "ETag", "Value": "W/\"yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:27:18 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "33384"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:27:18 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5503"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"DxXg5bXQ4gxLJqMY676LqOSwiLPgPBUr0RIcxebdQQs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:27:18 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DxXg5bXQ4gxLJqMY676LqOSwiLPgPBUr0RIcxebdQQs="}]}, {"Route": "index.o4tbd18gej.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000181686047"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5503"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"DxXg5bXQ4gxLJqMY676LqOSwiLPgPBUr0RIcxebdQQs=\""}, {"Name": "ETag", "Value": "W/\"yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:27:18 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o4tbd18gej"}, {"Name": "integrity", "Value": "sha256-yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.o4tbd18gej.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "33384"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:27:18 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o4tbd18gej"}, {"Name": "integrity", "Value": "sha256-yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.o4tbd18gej.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "5503"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"DxXg5bXQ4gxLJqMY676LqOSwiLPgPBUr0RIcxebdQQs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:27:18 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o4tbd18gej"}, {"Name": "integrity", "Value": "sha256-DxXg5bXQ4gxLJqMY676LqOSwiLPgPBUr0RIcxebdQQs="}, {"Name": "label", "Value": "index.html.gz"}]}]}