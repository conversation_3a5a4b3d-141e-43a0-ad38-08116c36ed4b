{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000149700599"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6679"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"F4H+K/3KE0Q24Jds5g3pj92O5rr6Ys2JcMD2CMt0+ic=\""}, {"Name": "ETag", "Value": "W/\"f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:35:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "39133"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:35:20 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6679"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"F4H+K/3KE0Q24Jds5g3pj92O5rr6Ys2JcMD2CMt0+ic=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:35:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F4H+K/3KE0Q24Jds5g3pj92O5rr6Ys2JcMD2CMt0+ic="}]}, {"Route": "index.vwmmhuk6e7.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000149700599"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6679"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"F4H+K/3KE0Q24Jds5g3pj92O5rr6Ys2JcMD2CMt0+ic=\""}, {"Name": "ETag", "Value": "W/\"f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:35:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vwmmhuk6e7"}, {"Name": "integrity", "Value": "sha256-f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.vwmmhuk6e7.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "39133"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:35:20 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vwmmhuk6e7"}, {"Name": "integrity", "Value": "sha256-f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.vwmmhuk6e7.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "6679"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"F4H+K/3KE0Q24Jds5g3pj92O5rr6Ys2JcMD2CMt0+ic=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:35:20 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vwmmhuk6e7"}, {"Name": "integrity", "Value": "sha256-F4H+K/3KE0Q24Jds5g3pj92O5rr6Ys2JcMD2CMt0+ic="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "vue-library.html", "AssetFile": "vue-library.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.045454545455"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "21"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=\""}, {"Name": "ETag", "Value": "W/\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}]}, {"Route": "vue-library.html", "AssetFile": "vue-library.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}]}, {"Route": "vue-library.html.gz", "AssetFile": "vue-library.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "21"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4="}]}, {"Route": "vue-library.lv5tvmajl9.html", "AssetFile": "vue-library.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.045454545455"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "21"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=\""}, {"Name": "ETag", "Value": "W/\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lv5tvmajl9"}, {"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}, {"Name": "label", "Value": "vue-library.html"}]}, {"Route": "vue-library.lv5tvmajl9.html", "AssetFile": "vue-library.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lv5tvmajl9"}, {"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}, {"Name": "label", "Value": "vue-library.html"}]}, {"Route": "vue-library.lv5tvmajl9.html.gz", "AssetFile": "vue-library.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "21"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lv5tvmajl9"}, {"Name": "integrity", "Value": "sha256-5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4="}, {"Name": "label", "Value": "vue-library.html.gz"}]}]}