using Microsoft.EntityFrameworkCore;
using LibraryManagement.Models;

namespace LibraryManagement.Data
{
    public class LibraryContext : DbContext
    {
        public LibraryContext(DbContextOptions<LibraryContext> options) : base(options)
        {
        }

        public DbSet<Book> Books { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Book entity
            modelBuilder.Entity<Book>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Author).IsRequired().HasMaxLength(100);
                entity.Property(e => e.ISBN).HasMaxLength(13);
                entity.Property(e => e.Publisher).HasMaxLength(100);
                entity.Property(e => e.Genre).HasMaxLength(50);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Rating).HasPrecision(3, 2);
            });

            // Seed data
            modelBuilder.Entity<Book>().HasData(
                new Book
                {
                    Id = 1,
                    Title = "Clean Code",
                    Author = "Robert C. Martin",
                    ISBN = "9780132350884",
                    Publisher = "Prentice Hall",
                    PublishedDate = new DateTime(2008, 8, 1),
                    Genre = "Programming",
                    PageCount = 464,
                    Description = "A handbook of agile software craftsmanship",
                    Rating = 4.5m,
                    IsAvailable = true,
                    CreatedAt = DateTime.UtcNow
                },
                new Book
                {
                    Id = 2,
                    Title = "Design Patterns",
                    Author = "Gang of Four",
                    ISBN = "9780201633610",
                    Publisher = "Addison-Wesley",
                    PublishedDate = new DateTime(1994, 10, 31),
                    Genre = "Programming",
                    PageCount = 395,
                    Description = "Elements of Reusable Object-Oriented Software",
                    Rating = 4.3m,
                    IsAvailable = true,
                    CreatedAt = DateTime.UtcNow
                },
                new Book
                {
                    Id = 3,
                    Title = "The Pragmatic Programmer",
                    Author = "Andrew Hunt, David Thomas",
                    ISBN = "9780201616224",
                    Publisher = "Addison-Wesley",
                    PublishedDate = new DateTime(1999, 10, 20),
                    Genre = "Programming",
                    PageCount = 352,
                    Description = "From Journeyman to Master",
                    Rating = 4.4m,
                    IsAvailable = false,
                    CreatedAt = DateTime.UtcNow
                },
                new Book
                {
                    Id = 4,
                    Title = "Effective C#",
                    Author = "Bill Wagner",
                    ISBN = "9780672337864",
                    Publisher = "Addison-Wesley",
                    PublishedDate = new DateTime(2016, 12, 30),
                    Genre = "Programming",
                    PageCount = 352,
                    Description = "50 Specific Ways to Improve Your C#",
                    Rating = 4.2m,
                    IsAvailable = true,
                    CreatedAt = DateTime.UtcNow
                },
                new Book
                {
                    Id = 5,
                    Title = "You Don't Know JS",
                    Author = "Kyle Simpson",
                    ISBN = "9781491924464",
                    Publisher = "O'Reilly Media",
                    PublishedDate = new DateTime(2015, 4, 27),
                    Genre = "Programming",
                    PageCount = 278,
                    Description = "Up & Going",
                    Rating = 4.1m,
                    IsAvailable = true,
                    CreatedAt = DateTime.UtcNow
                }
            );
        }
    }
}
