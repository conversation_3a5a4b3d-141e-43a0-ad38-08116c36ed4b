using System.ComponentModel.DataAnnotations;

namespace LibraryManagement.Models
{
    public class Book
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string Author { get; set; } = string.Empty;
        
        [StringLength(13)]
        public string? ISBN { get; set; }
        
        [StringLength(100)]
        public string? Publisher { get; set; }
        
        public DateTime? PublishedDate { get; set; }
        
        [StringLength(50)]
        public string? Genre { get; set; }
        
        [Range(1, 10000)]
        public int? PageCount { get; set; }
        
        [StringLength(1000)]
        public string? Description { get; set; }
        
        public string? CoverImageUrl { get; set; }
        
        [Range(0, 5)]
        public decimal? Rating { get; set; }
        
        public bool IsAvailable { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? UpdatedAt { get; set; }
    }
}
