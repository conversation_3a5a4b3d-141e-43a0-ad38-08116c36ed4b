{"Version": 1, "Hash": "8otRngRoJZs8A4h2gc0UOArQZKWuSz8TWZHddi5B1L0=", "Source": "TodoApi", "BasePath": "_content/TodoApi", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "TodoApi\\wwwroot", "Source": "TodoApi", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\wwwroot\\", "BasePath": "_content/TodoApi", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\TodoApi\\obj\\Debug\\net9.0\\compressed\\v7fzscjp8g-4yj483xvbo.gz", "SourceId": "TodoApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/TodoApi", "RelativePath": "index#[.{fingerprint=4yj483xvbo}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\TodoApi\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "61ct89n4xq", "Integrity": "Kh7e2Ht8JClF3q+XXwtIHEinkGoESXFZ4ZiBQDlrQJQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\TodoApi\\wwwroot\\index.html"}, {"Identity": "C:\\Users\\<USER>\\TodoApi\\wwwroot\\index.html", "SourceId": "TodoApi", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\wwwroot\\", "BasePath": "_content/TodoApi", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4yj483xvbo", "Integrity": "/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html"}], "Endpoints": [{"Route": "index.4yj483xvbo.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\obj\\Debug\\net9.0\\compressed\\v7fzscjp8g-4yj483xvbo.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000455166136"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2196"}, {"Name": "ETag", "Value": "\"Kh7e2Ht8JClF3q+XXwtIHEinkGoESXFZ4ZiBQDlrQJQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:17:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4yj483xvbo"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ="}]}, {"Route": "index.4yj483xvbo.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8577"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:17:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4yj483xvbo"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ="}]}, {"Route": "index.4yj483xvbo.html.gz", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\obj\\Debug\\net9.0\\compressed\\v7fzscjp8g-4yj483xvbo.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2196"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Kh7e2Ht8JClF3q+XXwtIHEinkGoESXFZ4ZiBQDlrQJQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:17:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4yj483xvbo"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-Kh7e2Ht8JClF3q+XXwtIHEinkGoESXFZ4ZiBQDlrQJQ="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\obj\\Debug\\net9.0\\compressed\\v7fzscjp8g-4yj483xvbo.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000455166136"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2196"}, {"Name": "ETag", "Value": "\"Kh7e2Ht8JClF3q+XXwtIHEinkGoESXFZ4ZiBQDlrQJQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:17:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8577"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:17:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\obj\\Debug\\net9.0\\compressed\\v7fzscjp8g-4yj483xvbo.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2196"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Kh7e2Ht8JClF3q+XXwtIHEinkGoESXFZ4ZiBQDlrQJQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:17:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kh7e2Ht8JClF3q+XXwtIHEinkGoESXFZ4ZiBQDlrQJQ="}]}]}