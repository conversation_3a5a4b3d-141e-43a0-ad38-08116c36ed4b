﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/LibraryManagement/create-sample-images.7kp85wy5fe.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\create-sample-images.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"7kp85wy5fe"},{"Name":"integrity","Value":"sha256-ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A="},{"Name":"label","Value":"_content/LibraryManagement/create-sample-images.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5394"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A=\u0022"},{"Name":"Last-Modified","Value":"Fri, 13 Jun 2025 03:45:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/LibraryManagement/create-sample-images.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\create-sample-images.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5394"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A=\u0022"},{"Name":"Last-Modified","Value":"Fri, 13 Jun 2025 03:45:33 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/LibraryManagement/index.ho564kftnr.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\index.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"ho564kftnr"},{"Name":"integrity","Value":"sha256-QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs="},{"Name":"label","Value":"_content/LibraryManagement/index.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"39263"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs=\u0022"},{"Name":"Last-Modified","Value":"Fri, 13 Jun 2025 03:45:00 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/LibraryManagement/index.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\index.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"39263"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs=\u0022"},{"Name":"Last-Modified","Value":"Fri, 13 Jun 2025 03:45:00 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/LibraryManagement/upload.hdb828yqv9.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\upload.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hdb828yqv9"},{"Name":"integrity","Value":"sha256-axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8="},{"Name":"label","Value":"_content/LibraryManagement/upload.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"5925"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8=\u0022"},{"Name":"Last-Modified","Value":"Fri, 13 Jun 2025 03:44:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/LibraryManagement/upload.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\upload.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"5925"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8=\u0022"},{"Name":"Last-Modified","Value":"Fri, 13 Jun 2025 03:44:29 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/LibraryManagement/vue-library.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\vue-library.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-AbpHGcgLb\u002BkRsJGnwFEktk7uzpZOCcBY74\u002BYBdrKVGs="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"1"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022AbpHGcgLb\u002BkRsJGnwFEktk7uzpZOCcBY74\u002BYBdrKVGs=\u0022"},{"Name":"Last-Modified","Value":"Fri, 13 Jun 2025 03:31:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/LibraryManagement/vue-library.lv5tvmajl9.html">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\vue-library.html'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"lv5tvmajl9"},{"Name":"integrity","Value":"sha256-AbpHGcgLb\u002BkRsJGnwFEktk7uzpZOCcBY74\u002BYBdrKVGs="},{"Name":"label","Value":"_content/LibraryManagement/vue-library.html"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"1"},{"Name":"Content-Type","Value":"text/html"},{"Name":"ETag","Value":"\u0022AbpHGcgLb\u002BkRsJGnwFEktk7uzpZOCcBY74\u002BYBdrKVGs=\u0022"},{"Name":"Last-Modified","Value":"Fri, 13 Jun 2025 03:31:37 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>