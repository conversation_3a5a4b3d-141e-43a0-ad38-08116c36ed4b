{"Files": [{"Id": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\index.html", "PackagePath": "staticwebassets\\index.html"}, {"Id": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\vue-library.html", "PackagePath": "staticwebassets\\vue-library.html"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.LibraryManagement.Microsoft.AspNetCore.StaticWebAssetEndpoints.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssetEndpoints.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.LibraryManagement.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.build.LibraryManagement.props", "PackagePath": "build\\LibraryManagement.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.buildMultiTargeting.LibraryManagement.props", "PackagePath": "buildMultiTargeting\\LibraryManagement.props"}, {"Id": "obj\\Debug\\net9.0\\staticwebassets\\msbuild.buildTransitive.LibraryManagement.props", "PackagePath": "buildTransitive\\LibraryManagement.props"}], "ElementsToRemove": []}