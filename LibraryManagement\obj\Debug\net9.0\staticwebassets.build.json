{"Version": 1, "Hash": "TvT5ml+eROl19Pb0ZumEjL3Qt40mbx3d9skX/7BBzyM=", "Source": "LibraryManagement", "BasePath": "_content/LibraryManagement", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "LibraryManagement\\wwwroot", "Source": "LibraryManagement", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\", "BasePath": "_content/LibraryManagement", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\5aes2t1s1r-vwmmhuk6e7.gz", "SourceId": "LibraryManagement", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LibraryManagement", "RelativePath": "index#[.{fingerprint=vwmmhuk6e7}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jpo0smhtxq", "Integrity": "F4H+K/3KE0Q24Jds5g3pj92O5rr6Ys2JcMD2CMt0+ic=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\index.html"}, {"Identity": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\o3ze3owncs-lv5tvmajl9.gz", "SourceId": "LibraryManagement", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LibraryManagement", "RelativePath": "vue-library#[.{fingerprint=lv5tvmajl9}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\vue-library.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e6kn9hfbme", "Integrity": "5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\vue-library.html"}, {"Identity": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\index.html", "SourceId": "LibraryManagement", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\", "BasePath": "_content/LibraryManagement", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vwmmhuk6e7", "Integrity": "f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html"}, {"Identity": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\vue-library.html", "SourceId": "LibraryManagement", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\", "BasePath": "_content/LibraryManagement", "RelativePath": "vue-library#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lv5tvmajl9", "Integrity": "AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\vue-library.html"}], "Endpoints": [{"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\5aes2t1s1r-vwmmhuk6e7.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000149700599"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6679"}, {"Name": "ETag", "Value": "\"F4H+K/3KE0Q24Jds5g3pj92O5rr6Ys2JcMD2CMt0+ic=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:35:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "39133"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:35:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\5aes2t1s1r-vwmmhuk6e7.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6679"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"F4H+K/3KE0Q24Jds5g3pj92O5rr6Ys2JcMD2CMt0+ic=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:35:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-F4H+K/3KE0Q24Jds5g3pj92O5rr6Ys2JcMD2CMt0+ic="}]}, {"Route": "index.vwmmhuk6e7.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\5aes2t1s1r-vwmmhuk6e7.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000149700599"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6679"}, {"Name": "ETag", "Value": "\"F4H+K/3KE0Q24Jds5g3pj92O5rr6Ys2JcMD2CMt0+ic=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:35:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vwmmhuk6e7"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y="}]}, {"Route": "index.vwmmhuk6e7.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "39133"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:35:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vwmmhuk6e7"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-f8ZfMcaaJodjA0ps7p4H68vWDKvUKpih1GwUCuNww5Y="}]}, {"Route": "index.vwmmhuk6e7.html.gz", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\5aes2t1s1r-vwmmhuk6e7.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6679"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"F4H+K/3KE0Q24Jds5g3pj92O5rr6Ys2JcMD2CMt0+ic=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:35:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vwmmhuk6e7"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-F4H+K/3KE0Q24Jds5g3pj92O5rr6Ys2JcMD2CMt0+ic="}]}, {"Route": "vue-library.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\o3ze3owncs-lv5tvmajl9.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.045454545455"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21"}, {"Name": "ETag", "Value": "\"5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}]}, {"Route": "vue-library.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\vue-library.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}]}, {"Route": "vue-library.html.gz", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\o3ze3owncs-lv5tvmajl9.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4="}]}, {"Route": "vue-library.lv5tvmajl9.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\o3ze3owncs-lv5tvmajl9.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.045454545455"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21"}, {"Name": "ETag", "Value": "\"5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lv5tvmajl9"}, {"Name": "label", "Value": "vue-library.html"}, {"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}]}, {"Route": "vue-library.lv5tvmajl9.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\vue-library.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lv5tvmajl9"}, {"Name": "label", "Value": "vue-library.html"}, {"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}]}, {"Route": "vue-library.lv5tvmajl9.html.gz", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\o3ze3owncs-lv5tvmajl9.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lv5tvmajl9"}, {"Name": "label", "Value": "vue-library.html.gz"}, {"Name": "integrity", "Value": "sha256-5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4="}]}]}