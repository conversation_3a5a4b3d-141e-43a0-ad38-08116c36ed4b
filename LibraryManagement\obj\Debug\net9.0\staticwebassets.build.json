{"Version": 1, "Hash": "agQedFGSdgOs1khfPjrMPDdDgqTud2M18J3C7X6Edsw=", "Source": "LibraryManagement", "BasePath": "_content/LibraryManagement", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "LibraryManagement\\wwwroot", "Source": "LibraryManagement", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\", "BasePath": "_content/LibraryManagement", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\5aes2t1s1r-ho564kftnr.gz", "SourceId": "LibraryManagement", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LibraryManagement", "RelativePath": "index#[.{fingerprint=ho564kftnr}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o2jixeooj2", "Integrity": "EGrqTN4Cf8xmhpzaeQYU5D+T3sY9Z03QpAC7i/Kq7i0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\index.html"}, {"Identity": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\o3ze3owncs-lv5tvmajl9.gz", "SourceId": "LibraryManagement", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LibraryManagement", "RelativePath": "vue-library#[.{fingerprint=lv5tvmajl9}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\vue-library.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "e6kn9hfbme", "Integrity": "5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\vue-library.html"}, {"Identity": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\qqfmopmww7-7kp85wy5fe.gz", "SourceId": "LibraryManagement", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LibraryManagement", "RelativePath": "create-sample-images#[.{fingerprint=7kp85wy5fe}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\create-sample-images.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mqzpzb6mbu", "Integrity": "LnBfrQqyHg9cZ2mPfuIrVFYrUOZwWahBVdme+0WAVsM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\create-sample-images.html"}, {"Identity": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\xl7t3e6j9i-hdb828yqv9.gz", "SourceId": "LibraryManagement", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LibraryManagement", "RelativePath": "upload#[.{fingerprint=hdb828yqv9}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\upload.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4vx4y1uzzg", "Integrity": "kNWgX1uuvweeH2Zd5pFK2vz/2gfTq+mJ22gbp8GgQG4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\upload.html"}, {"Identity": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\create-sample-images.html", "SourceId": "LibraryManagement", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\", "BasePath": "_content/LibraryManagement", "RelativePath": "create-sample-images#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7kp85wy5fe", "Integrity": "ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\create-sample-images.html"}, {"Identity": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\index.html", "SourceId": "LibraryManagement", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\", "BasePath": "_content/LibraryManagement", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ho564kftnr", "Integrity": "QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html"}, {"Identity": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\upload.html", "SourceId": "LibraryManagement", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\", "BasePath": "_content/LibraryManagement", "RelativePath": "upload#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hdb828yqv9", "Integrity": "axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\upload.html"}, {"Identity": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\vue-library.html", "SourceId": "LibraryManagement", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\", "BasePath": "_content/LibraryManagement", "RelativePath": "vue-library#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lv5tvmajl9", "Integrity": "AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\vue-library.html"}], "Endpoints": [{"Route": "create-sample-images.7kp85wy5fe.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\qqfmopmww7-7kp85wy5fe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000677506775"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1475"}, {"Name": "ETag", "Value": "\"LnBfrQqyHg9cZ2mPfuIrVFYrUOZwWahBVdme+0WAVsM=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7kp85wy5fe"}, {"Name": "label", "Value": "create-sample-images.html"}, {"Name": "integrity", "Value": "sha256-ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A="}]}, {"Route": "create-sample-images.7kp85wy5fe.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\create-sample-images.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5394"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7kp85wy5fe"}, {"Name": "label", "Value": "create-sample-images.html"}, {"Name": "integrity", "Value": "sha256-ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A="}]}, {"Route": "create-sample-images.7kp85wy5fe.html.gz", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\qqfmopmww7-7kp85wy5fe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1475"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"LnBfrQqyHg9cZ2mPfuIrVFYrUOZwWahBVdme+0WAVsM=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7kp85wy5fe"}, {"Name": "label", "Value": "create-sample-images.html.gz"}, {"Name": "integrity", "Value": "sha256-LnBfrQqyHg9cZ2mPfuIrVFYrUOZwWahBVdme+0WAVsM="}]}, {"Route": "create-sample-images.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\qqfmopmww7-7kp85wy5fe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000677506775"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1475"}, {"Name": "ETag", "Value": "\"LnBfrQqyHg9cZ2mPfuIrVFYrUOZwWahBVdme+0WAVsM=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A="}]}, {"Route": "create-sample-images.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\create-sample-images.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5394"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZziZAW/Hkv1KC6PZunrjdZhpnAacfLjDve4quIMkV8A="}]}, {"Route": "create-sample-images.html.gz", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\qqfmopmww7-7kp85wy5fe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1475"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"LnBfrQqyHg9cZ2mPfuIrVFYrUOZwWahBVdme+0WAVsM=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LnBfrQqyHg9cZ2mPfuIrVFYrUOZwWahBVdme+0WAVsM="}]}, {"Route": "index.ho564kftnr.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\5aes2t1s1r-ho564kftnr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000149409831"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6692"}, {"Name": "ETag", "Value": "\"EGrqTN4Cf8xmhpzaeQYU5D+T3sY9Z03QpAC7i/Kq7i0=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:00 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ho564kftnr"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs="}]}, {"Route": "index.ho564kftnr.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "39263"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:00 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ho564kftnr"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs="}]}, {"Route": "index.ho564kftnr.html.gz", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\5aes2t1s1r-ho564kftnr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6692"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"EGrqTN4Cf8xmhpzaeQYU5D+T3sY9Z03QpAC7i/Kq7i0=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:00 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ho564kftnr"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-EGrqTN4Cf8xmhpzaeQYU5D+T3sY9Z03QpAC7i/Kq7i0="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\5aes2t1s1r-ho564kftnr.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000149409831"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6692"}, {"Name": "ETag", "Value": "\"EGrqTN4Cf8xmhpzaeQYU5D+T3sY9Z03QpAC7i/Kq7i0=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "39263"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QeYBqp/SNUN5FdvpulHoE/WaiovduXCPP8Q3WJQJCfs="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\5aes2t1s1r-ho564kftnr.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6692"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"EGrqTN4Cf8xmhpzaeQYU5D+T3sY9Z03QpAC7i/Kq7i0=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:45:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EGrqTN4Cf8xmhpzaeQYU5D+T3sY9Z03QpAC7i/Kq7i0="}]}, {"Route": "upload.hdb828yqv9.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\xl7t3e6j9i-hdb828yqv9.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000514668039"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1942"}, {"Name": "ETag", "Value": "\"kNWgX1uuvweeH2Zd5pFK2vz/2gfTq+mJ22gbp8GgQG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:44:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hdb828yqv9"}, {"Name": "label", "Value": "upload.html"}, {"Name": "integrity", "Value": "sha256-axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8="}]}, {"Route": "upload.hdb828yqv9.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\upload.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5925"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:44:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hdb828yqv9"}, {"Name": "label", "Value": "upload.html"}, {"Name": "integrity", "Value": "sha256-axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8="}]}, {"Route": "upload.hdb828yqv9.html.gz", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\xl7t3e6j9i-hdb828yqv9.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1942"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"kNWgX1uuvweeH2Zd5pFK2vz/2gfTq+mJ22gbp8GgQG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:44:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hdb828yqv9"}, {"Name": "label", "Value": "upload.html.gz"}, {"Name": "integrity", "Value": "sha256-kNWgX1uuvweeH2Zd5pFK2vz/2gfTq+mJ22gbp8GgQG4="}]}, {"Route": "upload.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\xl7t3e6j9i-hdb828yqv9.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000514668039"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1942"}, {"Name": "ETag", "Value": "\"kNWgX1uuvweeH2Zd5pFK2vz/2gfTq+mJ22gbp8GgQG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:44:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8="}]}, {"Route": "upload.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\upload.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5925"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:44:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-axK8AViRlCHPn/XrJDKOl2sz4Fq7V0FLKG8Bf/z4uw8="}]}, {"Route": "upload.html.gz", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\xl7t3e6j9i-hdb828yqv9.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1942"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"kNWgX1uuvweeH2Zd5pFK2vz/2gfTq+mJ22gbp8GgQG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:44:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kNWgX1uuvweeH2Zd5pFK2vz/2gfTq+mJ22gbp8GgQG4="}]}, {"Route": "vue-library.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\o3ze3owncs-lv5tvmajl9.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.045454545455"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21"}, {"Name": "ETag", "Value": "\"5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}]}, {"Route": "vue-library.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\vue-library.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}]}, {"Route": "vue-library.html.gz", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\o3ze3owncs-lv5tvmajl9.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4="}]}, {"Route": "vue-library.lv5tvmajl9.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\o3ze3owncs-lv5tvmajl9.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.045454545455"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21"}, {"Name": "ETag", "Value": "\"5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lv5tvmajl9"}, {"Name": "label", "Value": "vue-library.html"}, {"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}]}, {"Route": "vue-library.lv5tvmajl9.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\vue-library.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lv5tvmajl9"}, {"Name": "label", "Value": "vue-library.html"}, {"Name": "integrity", "Value": "sha256-AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs="}]}, {"Route": "vue-library.lv5tvmajl9.html.gz", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\o3ze3owncs-lv5tvmajl9.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:31:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lv5tvmajl9"}, {"Name": "label", "Value": "vue-library.html.gz"}, {"Name": "integrity", "Value": "sha256-5gJ56tINJegD7ZFh7K9mn9pDMoPl9T0/sUigIukqqG4="}]}]}