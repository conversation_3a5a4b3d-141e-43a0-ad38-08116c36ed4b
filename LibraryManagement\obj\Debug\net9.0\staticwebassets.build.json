{"Version": 1, "Hash": "vllgAkY0PyAIEe1vSvaCaunZeKcCtaFRQ2KSkEcH32g=", "Source": "LibraryManagement", "BasePath": "_content/LibraryManagement", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "LibraryManagement\\wwwroot", "Source": "LibraryManagement", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\", "BasePath": "_content/LibraryManagement", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\5aes2t1s1r-o4tbd18gej.gz", "SourceId": "LibraryManagement", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/LibraryManagement", "RelativePath": "index#[.{fingerprint=o4tbd18gej}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0cldyv0v6", "Integrity": "DxXg5bXQ4gxLJqMY676LqOSwiLPgPBUr0RIcxebdQQs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\index.html"}, {"Identity": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\index.html", "SourceId": "LibraryManagement", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\", "BasePath": "_content/LibraryManagement", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o4tbd18gej", "Integrity": "yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html"}], "Endpoints": [{"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\5aes2t1s1r-o4tbd18gej.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000181686047"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5503"}, {"Name": "ETag", "Value": "\"DxXg5bXQ4gxLJqMY676LqOSwiLPgPBUr0RIcxebdQQs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:27:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33384"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:27:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\5aes2t1s1r-o4tbd18gej.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5503"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"DxXg5bXQ4gxLJqMY676LqOSwiLPgPBUr0RIcxebdQQs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:27:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DxXg5bXQ4gxLJqMY676LqOSwiLPgPBUr0RIcxebdQQs="}]}, {"Route": "index.o4tbd18gej.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\5aes2t1s1r-o4tbd18gej.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000181686047"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5503"}, {"Name": "ETag", "Value": "\"DxXg5bXQ4gxLJqMY676LqOSwiLPgPBUr0RIcxebdQQs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:27:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o4tbd18gej"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg="}]}, {"Route": "index.o4tbd18gej.html", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33384"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:27:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o4tbd18gej"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-yEbJyKtyfyyYp8OOoVSRLflxr+TpiaRhxUtgMCXXhUg="}]}, {"Route": "index.o4tbd18gej.html.gz", "AssetFile": "C:\\Users\\<USER>\\TodoApi\\LibraryManagement\\obj\\Debug\\net9.0\\compressed\\5aes2t1s1r-o4tbd18gej.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5503"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"DxXg5bXQ4gxLJqMY676LqOSwiLPgPBUr0RIcxebdQQs=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:27:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o4tbd18gej"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-DxXg5bXQ4gxLJqMY676LqOSwiLPgPBUr0RIcxebdQQs="}]}]}