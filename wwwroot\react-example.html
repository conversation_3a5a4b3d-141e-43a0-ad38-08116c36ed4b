<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todo App - React</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .app {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 40px;
            font-size: 2.5em;
        }
        .add-todo {
            display: flex;
            gap: 15px;
            margin-bottom: 40px;
        }
        input[type="text"] {
            flex: 1;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        input[type="text"]:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .btn-danger {
            background: #ff6b6b;
            color: white;
            padding: 8px 15px;
            font-size: 14px;
        }
        .btn-danger:hover {
            background: #ff5252;
        }
        .todo-list {
            list-style: none;
            padding: 0;
        }
        .todo-item {
            display: flex;
            align-items: center;
            padding: 20px;
            margin-bottom: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #667eea;
            transition: all 0.3s;
        }
        .todo-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .todo-item.completed {
            opacity: 0.7;
            border-left-color: #51cf66;
        }
        .todo-checkbox {
            margin-right: 20px;
            transform: scale(1.3);
            cursor: pointer;
        }
        .todo-text {
            flex: 1;
            font-size: 18px;
            color: #333;
        }
        .todo-item.completed .todo-text {
            text-decoration: line-through;
            color: #868e96;
        }
        .loading {
            text-align: center;
            color: #667eea;
            font-size: 18px;
            margin: 40px 0;
        }
        .error {
            color: #ff6b6b;
            background: #ffe0e0;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #ff6b6b;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .stat {
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        const API_BASE = 'http://localhost:5200/todoitems';

        function TodoApp() {
            const [todos, setTodos] = useState([]);
            const [newTodo, setNewTodo] = useState('');
            const [loading, setLoading] = useState(false);
            const [error, setError] = useState('');

            useEffect(() => {
                loadTodos();
            }, []);

            const loadTodos = async () => {
                setLoading(true);
                setError('');
                
                try {
                    const response = await fetch(API_BASE);
                    if (!response.ok) throw new Error('Không thể tải danh sách todo');
                    
                    const data = await response.json();
                    setTodos(data);
                } catch (err) {
                    setError('Lỗi khi tải danh sách: ' + err.message);
                } finally {
                    setLoading(false);
                }
            };

            const addTodo = async (e) => {
                e.preventDefault();
                if (!newTodo.trim()) {
                    setError('Vui lòng nhập tên công việc');
                    return;
                }

                setError('');
                
                try {
                    const response = await fetch(API_BASE, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            name: newTodo.trim(),
                            isComplete: false
                        })
                    });
                    
                    if (!response.ok) throw new Error('Không thể thêm todo');
                    
                    setNewTodo('');
                    loadTodos();
                } catch (err) {
                    setError('Lỗi khi thêm todo: ' + err.message);
                }
            };

            const toggleTodo = async (id, isComplete) => {
                setError('');
                
                try {
                    const todo = todos.find(t => t.id === id);
                    if (!todo) return;

                    const response = await fetch(`${API_BASE}/${id}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            id: id,
                            name: todo.name,
                            isComplete: isComplete
                        })
                    });
                    
                    if (!response.ok) throw new Error('Không thể cập nhật todo');
                    
                    loadTodos();
                } catch (err) {
                    setError('Lỗi khi cập nhật todo: ' + err.message);
                }
            };

            const deleteTodo = async (id) => {
                if (!window.confirm('Bạn có chắc muốn xóa công việc này?')) return;
                
                setError('');
                
                try {
                    const response = await fetch(`${API_BASE}/${id}`, {
                        method: 'DELETE'
                    });
                    
                    if (!response.ok) throw new Error('Không thể xóa todo');
                    
                    loadTodos();
                } catch (err) {
                    setError('Lỗi khi xóa todo: ' + err.message);
                }
            };

            const completedCount = todos.filter(todo => todo.isComplete).length;
            const totalCount = todos.length;
            const pendingCount = totalCount - completedCount;

            return (
                <div className="app">
                    <h1>📝 Todo App với React</h1>
                    
                    <div className="stats">
                        <div className="stat">
                            <div className="stat-number">{totalCount}</div>
                            <div className="stat-label">Tổng số</div>
                        </div>
                        <div className="stat">
                            <div className="stat-number">{pendingCount}</div>
                            <div className="stat-label">Chưa hoàn thành</div>
                        </div>
                        <div className="stat">
                            <div className="stat-number">{completedCount}</div>
                            <div className="stat-label">Đã hoàn thành</div>
                        </div>
                    </div>
                    
                    <form onSubmit={addTodo} className="add-todo">
                        <input
                            type="text"
                            value={newTodo}
                            onChange={(e) => setNewTodo(e.target.value)}
                            placeholder="Nhập công việc cần làm..."
                        />
                        <button type="submit" className="btn btn-primary">
                            Thêm
                        </button>
                    </form>
                    
                    {error && <div className="error">{error}</div>}
                    {loading && <div className="loading">Đang tải...</div>}
                    
                    <ul className="todo-list">
                        {todos.length === 0 && !loading ? (
                            <li style={{textAlign: 'center', color: '#666', fontStyle: 'italic', padding: '40px'}}>
                                Chưa có công việc nào
                            </li>
                        ) : (
                            todos.map(todo => (
                                <li key={todo.id} className={`todo-item ${todo.isComplete ? 'completed' : ''}`}>
                                    <input
                                        type="checkbox"
                                        className="todo-checkbox"
                                        checked={todo.isComplete}
                                        onChange={(e) => toggleTodo(todo.id, e.target.checked)}
                                    />
                                    <span className="todo-text">{todo.name}</span>
                                    <button
                                        className="btn btn-danger"
                                        onClick={() => deleteTodo(todo.id)}
                                    >
                                        Xóa
                                    </button>
                                </li>
                            ))
                        )}
                    </ul>
                </div>
            );
        }

        ReactDOM.render(<TodoApp />, document.getElementById('root'));
    </script>
</body>
</html>
