{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "index.4yj483xvbo.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000455166136"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2196"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Kh7e2Ht8JClF3q+XXwtIHEinkGoESXFZ4ZiBQDlrQJQ=\""}, {"Name": "ETag", "Value": "W/\"/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:17:38 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4yj483xvbo"}, {"Name": "integrity", "Value": "sha256-/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.4yj483xvbo.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8577"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:17:38 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4yj483xvbo"}, {"Name": "integrity", "Value": "sha256-/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.4yj483xvbo.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2196"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Kh7e2Ht8JClF3q+XXwtIHEinkGoESXFZ4ZiBQDlrQJQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:17:38 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4yj483xvbo"}, {"Name": "integrity", "Value": "sha256-Kh7e2Ht8JClF3q+XXwtIHEinkGoESXFZ4ZiBQDlrQJQ="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000455166136"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2196"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Kh7e2Ht8JClF3q+XXwtIHEinkGoESXFZ4ZiBQDlrQJQ=\""}, {"Name": "ETag", "Value": "W/\"/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:17:38 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8577"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:17:38 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/AHGfXJ5sxsHLjUN8FrRiIPSMkNTR+DIVONorTFBkTQ="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2196"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Kh7e2Ht8JClF3q+XXwtIHEinkGoESXFZ4ZiBQDlrQJQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 13 Jun 2025 03:17:38 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Kh7e2Ht8JClF3q+XXwtIHEinkGoESXFZ4ZiBQDlrQJQ="}]}]}