<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todo App - Vue.js</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #42b883 0%, #35495e 100%);
            min-height: 100vh;
        }
        .app {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 40px;
            font-size: 2.5em;
        }
        .filters {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 30px;
        }
        .filter-btn {
            padding: 10px 20px;
            border: 2px solid #42b883;
            background: white;
            color: #42b883;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .filter-btn.active {
            background: #42b883;
            color: white;
        }
        .add-todo {
            display: flex;
            gap: 15px;
            margin-bottom: 40px;
        }
        input[type="text"] {
            flex: 1;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        input[type="text"]:focus {
            outline: none;
            border-color: #42b883;
        }
        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #42b883;
            color: white;
        }
        .btn-primary:hover {
            background: #369870;
            transform: translateY(-2px);
        }
        .btn-danger {
            background: #e74c3c;
            color: white;
            padding: 8px 15px;
            font-size: 14px;
        }
        .btn-danger:hover {
            background: #c0392b;
        }
        .todo-list {
            list-style: none;
            padding: 0;
        }
        .todo-item {
            display: flex;
            align-items: center;
            padding: 20px;
            margin-bottom: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #42b883;
            transition: all 0.3s;
        }
        .todo-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .todo-item.completed {
            opacity: 0.7;
            border-left-color: #27ae60;
        }
        .todo-checkbox {
            margin-right: 20px;
            transform: scale(1.3);
            cursor: pointer;
        }
        .todo-text {
            flex: 1;
            font-size: 18px;
            color: #2c3e50;
        }
        .todo-item.completed .todo-text {
            text-decoration: line-through;
            color: #7f8c8d;
        }
        .loading {
            text-align: center;
            color: #42b883;
            font-size: 18px;
            margin: 40px 0;
        }
        .error {
            color: #e74c3c;
            background: #fadbd8;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #e74c3c;
        }
        .fade-enter-active, .fade-leave-active {
            transition: opacity 0.5s;
        }
        .fade-enter-from, .fade-leave-to {
            opacity: 0;
        }
        .slide-enter-active, .slide-leave-active {
            transition: all 0.3s;
        }
        .slide-enter-from {
            transform: translateX(-100%);
            opacity: 0;
        }
        .slide-leave-to {
            transform: translateX(100%);
            opacity: 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app">
            <h1>📝 Todo App với Vue.js</h1>
            
            <div class="filters">
                <button 
                    v-for="filter in filters" 
                    :key="filter.key"
                    @click="currentFilter = filter.key"
                    :class="['filter-btn', { active: currentFilter === filter.key }]"
                >
                    {{ filter.label }}
                </button>
            </div>
            
            <form @submit.prevent="addTodo" class="add-todo">
                <input
                    type="text"
                    v-model="newTodo"
                    placeholder="Nhập công việc cần làm..."
                    :disabled="loading"
                />
                <button type="submit" class="btn btn-primary" :disabled="loading">
                    {{ loading ? 'Đang thêm...' : 'Thêm' }}
                </button>
            </form>
            
            <transition name="fade">
                <div v-if="error" class="error">{{ error }}</div>
            </transition>
            
            <div v-if="loading && todos.length === 0" class="loading">
                Đang tải...
            </div>
            
            <transition-group name="slide" tag="ul" class="todo-list">
                <li v-if="filteredTodos.length === 0 && !loading" 
                    key="empty" 
                    style="text-align: center; color: #7f8c8d; font-style: italic; padding: 40px;">
                    {{ currentFilter === 'all' ? 'Chưa có công việc nào' : 
                       currentFilter === 'active' ? 'Không có công việc chưa hoàn thành' : 
                       'Không có công việc đã hoàn thành' }}
                </li>
                
                <li v-for="todo in filteredTodos" 
                    :key="todo.id" 
                    :class="['todo-item', { completed: todo.isComplete }]">
                    <input
                        type="checkbox"
                        class="todo-checkbox"
                        :checked="todo.isComplete"
                        @change="toggleTodo(todo.id, $event.target.checked)"
                    />
                    <span class="todo-text">{{ todo.name }}</span>
                    <button
                        class="btn btn-danger"
                        @click="deleteTodo(todo.id)"
                    >
                        Xóa
                    </button>
                </li>
            </transition-group>
            
            <div v-if="todos.length > 0" style="margin-top: 30px; text-align: center; color: #7f8c8d;">
                {{ completedCount }} / {{ totalCount }} công việc đã hoàn thành
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, computed, onMounted } = Vue;

        createApp({
            setup() {
                const todos = ref([]);
                const newTodo = ref('');
                const loading = ref(false);
                const error = ref('');
                const currentFilter = ref('all');

                const API_BASE = 'http://localhost:5200/todoitems';

                const filters = [
                    { key: 'all', label: 'Tất cả' },
                    { key: 'active', label: 'Chưa hoàn thành' },
                    { key: 'completed', label: 'Đã hoàn thành' }
                ];

                const filteredTodos = computed(() => {
                    switch (currentFilter.value) {
                        case 'active':
                            return todos.value.filter(todo => !todo.isComplete);
                        case 'completed':
                            return todos.value.filter(todo => todo.isComplete);
                        default:
                            return todos.value;
                    }
                });

                const completedCount = computed(() => 
                    todos.value.filter(todo => todo.isComplete).length
                );

                const totalCount = computed(() => todos.value.length);

                const loadTodos = async () => {
                    loading.value = true;
                    error.value = '';
                    
                    try {
                        const response = await fetch(API_BASE);
                        if (!response.ok) throw new Error('Không thể tải danh sách todo');
                        
                        const data = await response.json();
                        todos.value = data;
                    } catch (err) {
                        error.value = 'Lỗi khi tải danh sách: ' + err.message;
                    } finally {
                        loading.value = false;
                    }
                };

                const addTodo = async () => {
                    if (!newTodo.value.trim()) {
                        error.value = 'Vui lòng nhập tên công việc';
                        return;
                    }

                    error.value = '';
                    loading.value = true;
                    
                    try {
                        const response = await fetch(API_BASE, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                name: newTodo.value.trim(),
                                isComplete: false
                            })
                        });
                        
                        if (!response.ok) throw new Error('Không thể thêm todo');
                        
                        newTodo.value = '';
                        await loadTodos();
                    } catch (err) {
                        error.value = 'Lỗi khi thêm todo: ' + err.message;
                    } finally {
                        loading.value = false;
                    }
                };

                const toggleTodo = async (id, isComplete) => {
                    error.value = '';
                    
                    try {
                        const todo = todos.value.find(t => t.id === id);
                        if (!todo) return;

                        const response = await fetch(`${API_BASE}/${id}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                id: id,
                                name: todo.name,
                                isComplete: isComplete
                            })
                        });
                        
                        if (!response.ok) throw new Error('Không thể cập nhật todo');
                        
                        await loadTodos();
                    } catch (err) {
                        error.value = 'Lỗi khi cập nhật todo: ' + err.message;
                    }
                };

                const deleteTodo = async (id) => {
                    if (!confirm('Bạn có chắc muốn xóa công việc này?')) return;
                    
                    error.value = '';
                    
                    try {
                        const response = await fetch(`${API_BASE}/${id}`, {
                            method: 'DELETE'
                        });
                        
                        if (!response.ok) throw new Error('Không thể xóa todo');
                        
                        await loadTodos();
                    } catch (err) {
                        error.value = 'Lỗi khi xóa todo: ' + err.message;
                    }
                };

                onMounted(() => {
                    loadTodos();
                });

                return {
                    todos,
                    newTodo,
                    loading,
                    error,
                    currentFilter,
                    filters,
                    filteredTodos,
                    completedCount,
                    totalCount,
                    addTodo,
                    toggleTodo,
                    deleteTodo
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
