using Microsoft.EntityFrameworkCore;
using LibraryManagement.Data;
using LibraryManagement.Models;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddDbContext<LibraryContext>(opt =>
    opt.UseInMemoryDatabase("LibraryDb"));

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", builder =>
    {
        builder.AllowAnyOrigin()
               .AllowAnyMethod()
               .AllowAnyHeader();
    });
});

// Add API Explorer and Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

// Configure the HTTP request pipeline
app.UseCors("AllowAll");

// Serve static files
app.UseDefaultFiles();
app.UseStaticFiles();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Seed database
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<LibraryContext>();
    context.Database.EnsureCreated();
}

// API Endpoints
var books = app.MapGroup("/api/books").WithTags("Books");

books.MapGet("/", GetAllBooks);
books.MapGet("/{id}", GetBook);
books.MapGet("/search", SearchBooks);
books.MapPost("/", CreateBook);
books.MapPut("/{id}", UpdateBook);
books.MapDelete("/{id}", DeleteBook);

app.Run();

// API Methods
static async Task<IResult> GetAllBooks(LibraryContext db, int page = 1, int pageSize = 10)
{
    var totalBooks = await db.Books.CountAsync();
    var books = await db.Books
        .OrderBy(b => b.Title)
        .Skip((page - 1) * pageSize)
        .Take(pageSize)
        .Select(b => new BookDto(b))
        .ToListAsync();

    return TypedResults.Ok(new
    {
        books,
        totalBooks,
        page,
        pageSize,
        totalPages = (int)Math.Ceiling((double)totalBooks / pageSize)
    });
}

static async Task<IResult> GetBook(int id, LibraryContext db)
{
    var book = await db.Books.FindAsync(id);
    return book is not null
        ? TypedResults.Ok(new BookDto(book))
        : TypedResults.NotFound();
}

static async Task<IResult> SearchBooks(LibraryContext db, string? query = null, string? genre = null, bool? available = null)
{
    var booksQuery = db.Books.AsQueryable();

    if (!string.IsNullOrEmpty(query))
    {
        booksQuery = booksQuery.Where(b =>
            b.Title.Contains(query) ||
            b.Author.Contains(query) ||
            b.Description!.Contains(query));
    }

    if (!string.IsNullOrEmpty(genre))
    {
        booksQuery = booksQuery.Where(b => b.Genre == genre);
    }

    if (available.HasValue)
    {
        booksQuery = booksQuery.Where(b => b.IsAvailable == available.Value);
    }

    var books = await booksQuery
        .OrderBy(b => b.Title)
        .Select(b => new BookDto(b))
        .ToListAsync();

    return TypedResults.Ok(books);
}

static async Task<IResult> CreateBook(BookDto bookDto, LibraryContext db)
{
    var book = bookDto.ToBook();
    book.CreatedAt = DateTime.UtcNow;

    db.Books.Add(book);
    await db.SaveChangesAsync();

    return TypedResults.Created($"/api/books/{book.Id}", new BookDto(book));
}

static async Task<IResult> UpdateBook(int id, BookDto bookDto, LibraryContext db)
{
    var book = await db.Books.FindAsync(id);
    if (book is null) return TypedResults.NotFound();

    book.Title = bookDto.Title;
    book.Author = bookDto.Author;
    book.ISBN = bookDto.ISBN;
    book.Publisher = bookDto.Publisher;
    book.PublishedDate = bookDto.PublishedDate;
    book.Genre = bookDto.Genre;
    book.PageCount = bookDto.PageCount;
    book.Description = bookDto.Description;
    book.CoverImageUrl = bookDto.CoverImageUrl;
    book.Rating = bookDto.Rating;
    book.IsAvailable = bookDto.IsAvailable;
    book.UpdatedAt = DateTime.UtcNow;

    await db.SaveChangesAsync();
    return TypedResults.NoContent();
}

static async Task<IResult> DeleteBook(int id, LibraryContext db)
{
    var book = await db.Books.FindAsync(id);
    if (book is null) return TypedResults.NotFound();

    db.Books.Remove(book);
    await db.SaveChangesAsync();
    return TypedResults.NoContent();
}
